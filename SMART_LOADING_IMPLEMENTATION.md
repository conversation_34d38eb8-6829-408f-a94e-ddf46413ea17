# Smart Loading Implementation

## Overview

ShareFlix now includes **Smart Loading** functionality that intelligently selects relevant historical conversations based on user queries. This solves the context window limitation by prioritizing the most relevant conversations instead of just loading the most recent ones.

## Problem Solved

**Before**: The system loaded only the last 200 conversations, making it impossible to access valuable historical insights like <PERSON>'s message from 03/07/2025.

**After**: The system analyzes user queries and prioritizes relevant conversations, ensuring important historical context is available when needed.

## How It Works

### 🔍 **Keyword Extraction**

The system extracts relevant keywords from user messages:

- **People**: `robin maxwell`, `adam jonas`, `tfg`, `gr`, etc.
- **Companies**: `apple`, `tesla`, `spotify`, `bp`, `nvidia`, etc.
- **Investment Terms**: `stock`, `valuation`, `earnings`, `portfolio`, etc.
- **Topics**: `energy transition`, `ai`, `nuclear`, `crypto`, etc.

### 📊 **Relevance Scoring**

Each conversation gets a relevance score based on keyword matches:

- **People matches**: 10 points (highest priority)
- **Company matches**: 5 points
- **Topic matches**: 3 points  
- **Investment term matches**: 2 points

### 🎯 **Smart Selection**

The system selects conversations using a hybrid approach:

- **70% relevant**: Top-scored conversations based on query
- **30% recent**: Latest conversations for current context
- **Maximum 200**: Total conversations to stay within token limits

## Implementation Details

### 🔧 **Core Functions**

1. **`extractKeywords(userMessage)`**
   - Analyzes user input for relevant terms
   - Returns categorized keywords

2. **`calculateRelevanceScore(conversation, keywords)`**
   - Scores conversations based on keyword matches
   - Considers both content and author

3. **`getRelevantConversations(userMessage, maxConversations)`**
   - Main smart loading function
   - Returns prioritized conversation list

4. **`loadCleanedConversations(userMessage)`**
   - Updated to use smart loading
   - Formats conversations for AI context

### 📈 **Performance Results**

**Test Case**: "What did Robin Maxwell say about Spotify?"

- **Keywords Found**: `robin maxwell`, `spotify`, `spot`
- **Total Robin Maxwell conversations**: 37 (including March 2025 message)
- **Relevance scoring**: Successfully prioritizes Robin's messages
- **Historical access**: ✅ Can now access 03/07/2025 message

## Usage Examples

### 🎯 **Specific Person Query**
```
User: "What did Robin Maxwell say about Spotify?"
Result: Prioritizes Robin Maxwell's conversations + Spotify mentions
```

### 🏢 **Company-Focused Query**
```
User: "Tell me about Apple stock discussions"
Result: Prioritizes Apple/AAPL conversations + recent market context
```

### 📅 **Historical Context**
```
User: "What was discussed about energy transition?"
Result: Finds relevant energy discussions across all time periods
```

### 🔄 **Fallback Behavior**
```
User: "Hello, how are you?"
Result: No specific keywords → loads recent 200 conversations
```

## Technical Integration

### 🔗 **API Integration**

The smart loading is automatically integrated into the chat endpoint:

```javascript
// User message is passed to smart loading
const cleanedConversations = loadCleanedConversations(message);
```

### 📊 **Logging Output**

The system provides detailed logging:

```
🎯 Smart loading found 25 relevant + 15 recent conversations
🔍 Keywords: People: [robin maxwell], Companies: [spotify], Topics: []
✅ Loaded smart conversation history: 40 conversations
📊 Total characters: 15,234, Estimated tokens: 3,809
```

### 🎨 **AI Context Format**

Conversations are formatted with relevance scores:

```
**03/07/2025, 15:06:24** [july_collectivechat_data] - Robin Maxwell (Relevance: 15): "Just been on call with Hetal..."
```

## Benefits

### 🎯 **Improved Relevance**
- Historical insights now accessible
- Context-aware conversation selection
- Better answers to specific queries

### 📈 **Enhanced User Experience**
- Can ask about specific people/companies
- Access to full conversation timeline
- More comprehensive AI responses

### 🔧 **Technical Advantages**
- Stays within token limits
- Maintains recent context
- Scalable keyword system
- Automatic fallback behavior

## Configuration

### 🎛️ **Adjustable Parameters**

- **Max Conversations**: 200 (configurable)
- **Relevant/Recent Split**: 70%/30% (configurable)
- **Keyword Lists**: Easily expandable
- **Scoring Weights**: Adjustable per category

### 📝 **Keyword Management**

Keywords can be easily updated in the `extractKeywords()` function:

```javascript
const people = ['robin maxwell', 'adam jonas', 'new person'];
const companies = ['apple', 'tesla', 'new company'];
```

## Future Enhancements

### 🚀 **Potential Improvements**

1. **Machine Learning**: Train models on conversation relevance
2. **Semantic Search**: Use embeddings for better matching
3. **User Preferences**: Learn from user interaction patterns
4. **Dynamic Keywords**: Auto-extract important terms from conversations
5. **Time-based Weighting**: Adjust relevance based on recency

### 📊 **Analytics Opportunities**

- Track which keywords are most effective
- Monitor smart loading performance
- Analyze user query patterns
- Optimize relevance scoring algorithms

## Status

✅ **Implemented and Active**
- Smart keyword extraction
- Relevance-based scoring
- Hybrid conversation selection
- Automatic integration with chat API
- Comprehensive logging and monitoring

🎯 **Ready for Production**
- Tested with Robin Maxwell queries
- Verified historical access (March 2025)
- Maintains token efficiency
- Graceful fallback behavior

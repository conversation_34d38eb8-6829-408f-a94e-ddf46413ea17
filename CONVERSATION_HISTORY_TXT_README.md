# Conversation History Text File Feature

## Overview

The Echo Voice Leads application now saves conversation history in **both** JSON and TXT formats:

- **JSON Format** (`data/conversation_history.json`): Encrypted format for system use
- **TXT Format** (`data/conversation_history.txt`): Human-readable format for easy review

## Features

### Automatic Generation
- Every new conversation automatically updates both files
- Text file is regenerated completely each time to ensure consistency
- Maintains the same security and encryption for the JSON file

### Text File Format
The text file includes:
- Header with generation timestamp and total conversation count
- Each conversation clearly separated with dividers
- Conversation metadata (timestamp, session ID, user ID)
- Lead data (if present)
- User message and AI response in plain text
- Professional formatting for easy reading

### Example Format
```
================================================================================
ECHO VOICE LEADS - CONVERSATION HISTORY
================================================================================
Generated: 8/19/2025, 12:03:14 PM
Total Conversations: 10
================================================================================

CONVERSATION #1
----------------------------------------
Timestamp: 8/15/2025, 10:30:33 AM
Session ID: 1755250233339
User ID: Anonymous
Lead Data: {}

USER: Hello, how can you help me with investments?

AI RESPONSE: I'm Sarah from Premier Stock Advisory. I'd be happy to help you...

================================================================================
```

## API Endpoints

### Download Text File
- **Endpoint**: `GET /api/conversation-history/download`
- **Description**: Downloads the conversation history as a text file
- **Authentication**: Requires access password
- **Response**: Text file with filename `conversation_history_YYYY-MM-DD.txt`

### Clear History
- **Endpoint**: `DELETE /api/conversation-history`
- **Description**: Clears both JSON and TXT conversation history files
- **Authentication**: Requires access password

## File Locations

- **JSON File**: `data/conversation_history.json` (encrypted)
- **TXT File**: `data/conversation_history.txt` (plain text, human-readable)

## Security Notes

- The JSON file remains encrypted for security
- The TXT file is stored in plain text for readability
- Both files are stored in the `data/` directory
- Access to download requires proper authentication
- Files are automatically managed (no manual intervention needed)

## Benefits

1. **Easy Review**: Human-readable format for reviewing conversations
2. **Backup**: Additional format for data preservation
3. **Analysis**: Easier to analyze conversation patterns and content
4. **Sharing**: Can be easily shared or exported for review
5. **Debugging**: Helpful for troubleshooting conversation issues

## Implementation Details

- Text file is completely regenerated on each conversation save
- Maintains chronological order of conversations
- Includes all conversation metadata
- Handles HTML entities properly (e.g., `&#x27;` for apostrophes)
- Automatically limits to last 100 conversations (same as JSON)

## Usage

The feature works automatically - no configuration needed. Every time a user has a conversation:

1. The conversation is encrypted and saved to the JSON file
2. All conversations are decrypted and saved to the TXT file
3. Both files are kept in sync automatically

To download the text file, users can access the download endpoint through the web interface or API.
